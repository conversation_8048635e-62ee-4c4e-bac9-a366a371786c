1. **照片上传功能**：
   - 在新增会员表单中添加照片上传组件
   - 参考 `web/src/views/help/order/edit.vue` 组件中的文件上传实现方式
   - 使用现有的 upload 组件，设置 `cid: 0` 参数
   - 支持图片格式预览和上传

2. **会员卡类型数据结构调整**：
   - 将会员卡类型从字符串改为数字表示：
     - 年卡：使用数字 `1` 表示
     - 季卡：使用数字 `2` 表示
     - 月卡：使用数字 `3` 表示
     - 周卡：使用数字 `4` 表示
     - 次卡：使用数字 `5` 表示
   - 更新前端表单、后端模型和数据库结构

3. **次卡特殊功能**：
   - 当会员卡类型选择为次卡（值为 `2`）时，显示次数编辑字段
   - 添加次数余额字段，用于记录次卡剩余使用次数
   - 在表单验证中确保次卡类型时次数字段为必填且大于0

请确保前后端数据结构保持一致，并更新相关的API接口和数据模型。


消费模块
搜索栏输入手机号后4位，展示会员信息。如果有两位顾客手机号后四位重复，默认显示第一位顾客的信息，如果第二位顾客来了，则需要输入全部11位手机号

会员信息 可以直接快速操作内容如1. 游戏币增减 2.次卡客户次数增减

涉及到会员时常操作需要去会员管理模块去操作，到期时间，卡类型更改
