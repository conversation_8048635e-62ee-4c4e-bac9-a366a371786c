#### 拓疆者智控平台

*   ✨ [在线预览](http://oms.apps.builderx.com/)

*   📦 [下载地址](https://e.gitee.com/builderxinc/repos/bux-server/bux-admin-web)

*   📖 [相关文档](https://www.feishu.cn/drive/folder/fldcn2Imdjvn4zCclWn9mAYnVph)
  
##### 项目介绍

请帮我实现一个完整的会员管理模块，包含前端和后端代码。

## 后端要求：
1. 在 `api\apps\router\` 目录下创建 `member.py` 路由文件
2. 参考现有的 `customer.py` 和 `material.py` 文件的代码结构和风格
3. 使用 FastAPI 框架，包含 `@unified_resp` 装饰器
4. 实现完整的 CRUD 操作（增删改查）

## 数据模型字段：
1. **姓名** (name): 字符串类型，必填
2. **手机号** (phone): 字符串类型，必填，需要格式验证
3. **会员卡类型** (card_type): 枚举类型，只能选择 "年卡" 或 "次卡"
4. **会员到期时间** (expire_time): 日期时间类型
5. **游戏币余额** (coin_balance): 数字类型，默认值为 0
6. **会员照片** (photo): 文件上传，利用现有的素材管理模块 (`api\apps\router\material.py`) 来处理图片上传和存储

## API 接口要求：
- GET `/member` - 获取会员列表（支持分页和搜索）
- POST `/member` - 新增会员
- PUT `/member/{member_id}` - 更新会员信息
- DELETE `/member/{member_id}` - 删除会员
- GET `/member/{member_id}` - 获取单个会员详情
- POST `/member/upload_photo` - 上传会员照片（集成素材管理）

## 前端要求：
1. 参考现有前端代码结构，在 `web\src\api\` 目录下创建会员管理相关的 API 调用文件
2. 创建会员管理页面，包含：
   - 会员列表展示（表格形式）
   - 新增会员表单
   - 编辑会员表单
   - 删除确认
   - 照片上传预览功能
   - 搜索和筛选功能

## 技术要求：
- 后端使用现有的数据库连接和认证机制
- 前端使用现有的请求封装和组件库
- 照片上传复用现有的素材管理功能
- 遵循现有代码的命名规范和文件结构
- 添加适当的错误处理和数据验证

BUX-ADMIN-WEB是智能管控中心的前端项目，基于Vue3+ElementPlus开发，主要包含以下功能：

*   设备管理：设备列表、设备添加、设备编辑、设备删除

*   素材中心：录像在线查看、录像下载、录像打包

*   数据总览：设备在线统计、设备告警统计、设备类型统计、设备区域统计

*   日志查看：操作日志、告警日志

*   轨迹回放：查看指定设备指定时间段的运行轨迹

*   安装包管理：安装包列表、安装包上传、安装包删除

*   用户管理：用户列表、用户添加、用户编辑、用户删除、用户重置密码、用户分配角色

*   角色管理：角色列表、角色添加、角色编辑、角色删除、角色分配权限

*   权限管理：权限列表、权限添加、权限编辑、权限删除


##### 使用说明

1.  安装依赖

    ```bash
    npm install
    ```

2.  启动项目

    ```bash
    npm run dev
    ```

3.  打包项目

    ```bash
    npm run build
    ```

##### Docker说明
    
1.  构建镜像(注意 .)

    ```bash
    docker build -t <image_name>:<tag> .
    ```

2.  运行容器

    ```bash
    # -p 指定外部端 8080 于容器内 80端口连接
    # -d 表示在后台运行
    docker run --name <container_name> -d -p 8080:80 <image_name>:<tag>
    ```

#### Git分支管理

*   `master` 主分支，用于发布正式版本，不能直接在该分支上开发

*   `develop` 开发分支，用于日常开发，不能直接在该分支上开发

*   每次提交代码前，先从`develop`分支拉取最新代码，再提交代码

*   每次提交代码时，必须填写提交信息，提交信息按照git提交规范提交，详见下方

#### Git提交规范

*   `feat` 添加新功能

*   `fix` 修复问题

*   `style` 代码格式化

*   `perf` 性能优化

*   `refactor` 重构

*   `revert` 撤消编辑

*   `test` 测试用例等

*   `docs` 文档注释等

*   `chore` 依赖更新/脚手架配置修改等

*   `workflow` 工作流程改进

*   `types` 类型文件修改
