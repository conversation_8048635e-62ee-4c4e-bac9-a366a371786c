<template>
  <div class="member-management">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item label="搜索">
          <el-input 
            v-model="formData.keyword" 
            class="w-[200px]" 
            placeholder="姓名或手机号"
            clearable 
            @keyup.enter="resetPage" 
          />
        </el-form-item>
        <el-form-item label="会员卡类型">
          <el-select 
            v-model="formData.card_type" 
            class="w-[120px]" 
            placeholder="全部"
            clearable
          >
            <el-option label="年卡" value="年卡" />
            <el-option label="次卡" value="次卡" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="formData.is_expired" 
            class="w-[120px]" 
            placeholder="全部"
            clearable
          >
            <el-option label="正常" :value="false" />
            <el-option label="已过期" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增会员
        </el-button>
      </div>
      
      <div class="mt-4">
        <el-table :data="pager.lists" size="large" :height="calcTableHeight()">
          <el-table-column label="头像" width="80" align="center">
            <template #default="{ row }">
              <el-avatar 
                :size="50" 
                :src="row.photo" 
                :icon="UserFilled"
                shape="square"
              />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名" min-width="100" />
          <el-table-column prop="phone" label="手机号" min-width="120" />
          <el-table-column prop="card_type" label="会员卡类型" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.card_type === '年卡' ? 'success' : 'warning'">
                {{ row.card_type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="coin_balance" label="游戏币余额" width="120" align="center">
            <template #default="{ row }">
              <span class="text-orange-500 font-bold">{{ row.coin_balance }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="expire_time" label="到期时间" width="180" align="center">
            <template #default="{ row }">
              <span v-if="row.expire_time">
                {{ formatDateTime(row.expire_time) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_expired ? 'danger' : 'success'">
                {{ row.is_expired ? '已过期' : '正常' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click="handleUploadPhoto(row)">上传照片</el-button>
              <el-button type="danger" link @click="handleDelete(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="flex justify-end mt-4">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
    
    <!-- 照片上传弹窗 -->
    <photo-upload v-if="showPhotoUpload" ref="photoUploadRef" @success="getLists" @close="showPhotoUpload = false" />
  </div>
</template>

<script lang="ts" setup name="memberManagement">
import { UserFilled } from '@element-plus/icons-vue'
import { usePaging } from "@/hooks/usePaging"
import feedback from "@/utils/feedback"
import Pagination from "@/components/pagination/index.vue"
import Icon from "@/components/icon/index.vue"
import EditPopup from "./edit.vue"
import PhotoUpload from "./photo-upload.vue"
import { memberList, memberDelete } from "@/api/member"

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const photoUploadRef = shallowRef<InstanceType<typeof PhotoUpload>>()

// 表单数据
const formData = reactive({
  keyword: "",
  card_type: "",
  is_expired: null as boolean | null,
})

const showEdit = ref(false)
const showPhotoUpload = ref(false)

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: memberList,
  params: formData,
})

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 新增会员
const handleAdd = () => {
  showEdit.value = true
  nextTick(() => {
    editRef.value?.open()
  })
}

// 编辑会员
const handleEdit = (row: any) => {
  showEdit.value = true
  nextTick(() => {
    editRef.value?.open(row)
  })
}

// 上传照片
const handleUploadPhoto = (row: any) => {
  showPhotoUpload.value = true
  nextTick(() => {
    photoUploadRef.value?.open(row)
  })
}

// 删除会员
const handleDelete = async (id: string) => {
  await feedback.confirm('确定要删除该会员吗？')
  await memberDelete(id)
  feedback.msgSuccess('删除成功')
  getLists()
}

onMounted(() => {
  getLists()
})
</script>

<style scoped>
.member-management {
  padding: 0;
}
</style>
